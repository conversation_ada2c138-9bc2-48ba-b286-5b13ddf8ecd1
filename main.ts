import { z } from "zod";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";

// Create MCP server instance
const server = new McpServer({
  name: "weather-mcp-server",
  version: "1.0.0",
});

// Define tool: get-weather
server.tool(
  "get-weather",
  "Get the current weather for a city",
  { city: z.string().describe("City name, e.g. 'Berlin'") },
  async ({ city }) => {
    // Start with a simple static response, then fetch live data
    try {
      // 1) Geocode city -> lat/lon
      const geoRes = await fetch(
        `https://geocoding-api.open-meteo.com/v1/search?name=${encodeURIComponent(
          city
        )}&count=1`,
      );
      if (!geoRes.ok) {
        throw new Error(`Geocoding request failed with ${geoRes.status}`);
      }
      const geo = await geoRes.json();
      if (!geo.results || geo.results.length === 0) {
        return {
          content: [
            { type: "text", text: `No matching location found for "${city}".` },
          ],
        };
      }
      const best = geo.results[0];
      const { latitude, longitude, name, country } = best;

      // 2) Fetch current weather
      const weatherRes = await fetch(
        `https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&current_weather=true`,
      );
      if (!weatherRes.ok) {
        throw new Error(`Weather request failed with ${weatherRes.status}`);
      }
      const weather = await weatherRes.json();
      const cw = weather.current_weather;

      let text = `Weather for ${name}${country ? ", " + country : ""} (lat ${latitude}, lon ${longitude})`;
      if (cw) {
        text += `\n- Temperature: ${cw.temperature}°C`;
        text += `\n- Wind: ${cw.windspeed} km/h`;
        if (typeof cw.weathercode === "number") {
          text += `\n- Weather code: ${cw.weathercode}`;
        }
      } else {
        text += `\nNo current weather data available.`;
      }

      return { content: [{ type: "text", text }] };
    } catch (err) {
      const msg = err instanceof Error ? err.message : String(err);
      return {
        content: [
          { type: "text", text: `Error fetching weather for "${city}": ${msg}` },
        ],
      };
    }
  },
);

async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.log("MCP server is running on stdio...");
}

main().catch((err) => {
  console.error(err);
  process.exit(1);
});
