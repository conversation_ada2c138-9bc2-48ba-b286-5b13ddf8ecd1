import { z } from "zod";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { chromium, <PERSON><PERSON><PERSON>, <PERSON> } from "playwright";

// Create MCP server instance
const server = new McpServer({
  name: "web-testing-agent",
  version: "1.0.0",
});

// Global browser instance for reuse
let browser: Browser | null = null;

async function getBrowser(): Promise<Browser> {
  if (!browser) {
    browser = await chromium.launch({ headless: false }); // Set to true for headless mode
  }
  return browser;
}

// Tool 1: Navigate to a website
server.tool(
  "navigate-to-website",
  "Navigate to a website and take a screenshot",
  {
    url: z.string().describe("Website URL to navigate to"),
    waitForSelector: z.string().optional().describe("CSS selector to wait for before taking screenshot")
  },
  async ({ url, waitForSelector }) => {
    try {
      const browser = await getBrowser();
      const page = await browser.newPage();

      await page.goto(url, { waitUntil: 'networkidle' });

      if (waitForSelector) {
        await page.waitForSelector(waitForSelector, { timeout: 10000 });
      }

      const screenshot = await page.screenshot({ fullPage: true });
      const screenshotBase64 = screenshot.toString('base64');

      await page.close();

      return {
        content: [
          { type: "text", text: `Successfully navigated to ${url}` },
          {
            type: "image",
            data: screenshotBase64,
            mimeType: "image/png"
          }
        ]
      };
    } catch (err) {
      const msg = err instanceof Error ? err.message : String(err);
      return {
        content: [{ type: "text", text: `Error navigating to ${url}: ${msg}` }]
      };
    }
  }
);

// Tool 2: Fill and submit a form
server.tool(
  "fill-form",
  "Fill out and submit a form on a website",
  {
    url: z.string().describe("Website URL containing the form"),
    formData: z.record(z.string()).describe("Form field data as key-value pairs (selector: value)"),
    submitSelector: z.string().optional().describe("CSS selector for submit button (defaults to 'button[type=submit], input[type=submit]')")
  },
  async ({ url, formData, submitSelector = 'button[type="submit"], input[type="submit"]' }) => {
    try {
      const browser = await getBrowser();
      const page = await browser.newPage();

      await page.goto(url, { waitUntil: 'networkidle' });

      // Fill form fields
      const results: string[] = [];
      for (const [selector, value] of Object.entries(formData)) {
        try {
          await page.fill(selector, value);
          results.push(`✅ Filled ${selector} with "${value}"`);
        } catch (err) {
          results.push(`❌ Failed to fill ${selector}: ${err instanceof Error ? err.message : String(err)}`);
        }
      }

      // Take screenshot before submit
      const beforeScreenshot = await page.screenshot({ fullPage: true });

      // Submit form
      try {
        await page.click(submitSelector);
        await page.waitForLoadState('networkidle');
        results.push(`✅ Form submitted successfully`);
      } catch (err) {
        results.push(`❌ Failed to submit form: ${err instanceof Error ? err.message : String(err)}`);
      }

      // Take screenshot after submit
      const afterScreenshot = await page.screenshot({ fullPage: true });

      await page.close();

      return {
        content: [
          { type: "text", text: `Form testing results:\n${results.join('\n')}` },
          {
            type: "image",
            data: beforeScreenshot.toString('base64'),
            mimeType: "image/png"
          },
          {
            type: "image",
            data: afterScreenshot.toString('base64'),
            mimeType: "image/png"
          }
        ]
      };
    } catch (err) {
      const msg = err instanceof Error ? err.message : String(err);
      return {
        content: [{ type: "text", text: `Error testing form on ${url}: ${msg}` }]
      };
    }
  }
);

// Tool 3: Test website elements and interactions
server.tool(
  "test-website-elements",
  "Test various elements on a website (click buttons, check text, etc.)",
  {
    url: z.string().describe("Website URL to test"),
    actions: z.array(z.object({
      type: z.enum(["click", "check_text", "check_element_exists", "wait_for_element"]),
      selector: z.string().describe("CSS selector for the element"),
      expectedText: z.string().optional().describe("Expected text content (for check_text)"),
      timeout: z.number().optional().describe("Timeout in milliseconds (default: 5000)")
    })).describe("Array of actions to perform")
  },
  async ({ url, actions }) => {
    try {
      const browser = await getBrowser();
      const page = await browser.newPage();

      await page.goto(url, { waitUntil: 'networkidle' });

      const results: string[] = [];

      for (const action of actions) {
        const timeout = action.timeout || 5000;

        try {
          switch (action.type) {
            case "click":
              await page.click(action.selector, { timeout });
              results.push(`✅ Clicked element: ${action.selector}`);
              break;

            case "check_text":
              const element = page.locator(action.selector).first();
              const text = await element.textContent();
              if (text?.includes(action.expectedText || "")) {
                results.push(`✅ Text check passed for ${action.selector}: "${text}"`);
              } else {
                results.push(`❌ Text check failed for ${action.selector}. Expected: "${action.expectedText}", Got: "${text}"`);
              }
              break;

            case "check_element_exists":
              const exists = await page.locator(action.selector).count() > 0;
              if (exists) {
                results.push(`✅ Element exists: ${action.selector}`);
              } else {
                results.push(`❌ Element not found: ${action.selector}`);
              }
              break;

            case "wait_for_element":
              await page.waitForSelector(action.selector, { timeout });
              results.push(`✅ Element appeared: ${action.selector}`);
              break;
          }
        } catch (err) {
          results.push(`❌ Action ${action.type} failed for ${action.selector}: ${err instanceof Error ? err.message : String(err)}`);
        }
      }

      // Take final screenshot
      const screenshot = await page.screenshot({ fullPage: true });

      await page.close();

      return {
        content: [
          { type: "text", text: `Website testing results:\n${results.join('\n')}` },
          {
            type: "image",
            data: screenshot.toString('base64'),
            mimeType: "image/png"
          }
        ]
      };
    } catch (err) {
      const msg = err instanceof Error ? err.message : String(err);
      return {
        content: [{ type: "text", text: `Error testing website ${url}: ${msg}` }]
      };
    }
  }
);

// Cleanup function
async function cleanup() {
  if (browser) {
    await browser.close();
    browser = null;
  }
}

// Handle process termination
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);

async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.log("MCP server is running on stdio...");
}

main().catch(async (err) => {
  console.error(err);
  await cleanup();
  process.exit(1);
});
